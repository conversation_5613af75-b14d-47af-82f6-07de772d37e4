{"_from": "crypto-js", "_id": "crypto-js@4.2.0", "_inBundle": false, "_integrity": "sha512-KALDyEYgpY+Rlob/iriUtjV6d5Eq+Y191A5g4UqLAi8CyGP9N1+FdVbkc1SxKc2r4YAYqG8JzO2KGL+AizD70Q==", "_location": "/crypto-js", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "crypto-js", "name": "crypto-js", "escapedName": "crypto-js", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmjs.org/crypto-js/-/crypto-js-4.2.0.tgz", "_shasum": "4d931639ecdfd12ff80e8186dba6af2c2e856631", "_spec": "crypto-js", "_where": "E:\\workspace\\web\\ycursor-aug-code", "author": {"name": "<PERSON>", "url": "http://github.com/evanvosberg"}, "browser": {"crypto": false}, "bugs": {"url": "https://github.com/brix/crypto-js/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "JavaScript library of crypto standards.", "homepage": "http://github.com/brix/crypto-js", "keywords": ["security", "crypto", "Hash", "MD5", "SHA1", "SHA-1", "SHA256", "SHA-256", "RC4", "Rabbit", "AES", "DES", "PBKDF2", "HMAC", "OFB", "CFB", "CTR", "CBC", "Base64", "Base64url"], "license": "MIT", "main": "index.js", "name": "crypto-js", "repository": {"type": "git", "url": "git+ssh://**************/brix/crypto-js.git"}, "version": "4.2.0"}