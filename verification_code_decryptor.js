/**
 * 微信小程序验证码获取和解密工具
 * 基于对小程序代码的逆向分析实现
 */

const CryptoJS = require('crypto-js');
const axios = require('axios');

class VerificationCodeDecryptor {
    constructor() {
        this.apiSignatureSecret = "";
        this.apiKeyVersion = 0;
        this.apiKeyLastUpdate = 0;
        this.sessionId = "";
        this.tempToken = "";
        this.baseURL = "https://app.yan.vin";

        // 备用签名密钥（从服务器获取的真实密钥）
        this.fallbackSecret = "YAN_API_SIGN_V5_2025_MILITARY_GRADE_KEY_9876543210fedcba0987654321abcdef";
    }

    /**
     * 获取API签名密钥
     */
    async fetchCurrentSignatureKey() {
        const now = Date.now();

        // 如果密钥存在且未过期（5分钟），直接返回
        if (this.apiSignatureSecret && (now - this.apiKeyLastUpdate) < 300000) {
            return;
        }

        try {
            const response = await axios.get(`${this.baseURL}/api/public/signature-key`, {
                headers: {
                    'Accept': 'application/json, text/plain, */*',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090819)',
                    'Referer': 'https://servicewechat.com/wx421aabd7feefa0ed/devtools/page-frame.html',
                    'Origin': 'https://servicewechat.com',
                    'Connection': 'keep-alive',
                    'Sec-Fetch-Dest': 'empty',
                    'Sec-Fetch-Mode': 'cors',
                    'Sec-Fetch-Site': 'cross-site'
                },
                timeout: 10000
            });

            if (response.status === 200 && response.data) {
                if (response.data.key && response.data.version) {
                    this.apiSignatureSecret = response.data.key;
                    this.apiKeyVersion = response.data.version;
                    this.apiKeyLastUpdate = now;
                    console.log(`获取签名密钥成功: version ${this.apiKeyVersion}`);
                } else {
                    throw new Error("响应数据格式错误");
                }
            } else {
                throw new Error(`HTTP状态码: ${response.status}`);
            }
        } catch (error) {
            console.error("获取签名密钥失败，使用备用密钥:", error.message);
            this.apiSignatureSecret = this.fallbackSecret;
            this.apiKeyVersion = 1;
            this.apiKeyLastUpdate = now;
        }
    }

    /**
     * 生成设备指纹
     */
    generateDeviceFingerprint() {
        try {
            // 模拟真实的微信小程序环境
            const fingerprint = {
                model: "microsoft",
                platform: "windows",
                version: "3.9.10",
                system: "Windows 10 x64",
                language: "zh_CN",
                screenWidth: 1920,
                screenHeight: 1080,
                windowWidth: 1920,
                windowHeight: 1080,
                pixelRatio: 1,
                appId: "wx421aabd7feefa0ed",
                envVersion: "release",
                timestamp: Date.now(),
                random: Math.random(),
                statusBarHeight: 0,
                safeArea: {
                    top: 0,
                    bottom: 1080,
                    left: 0,
                    right: 1920
                },
                networkType: "wifi",
                benchMark: 1,
                brand: "microsoft"
            };

            const fingerprintStr = JSON.stringify(fingerprint);
            return CryptoJS.SHA256(fingerprintStr).toString();
        } catch (error) {
            // 备用指纹生成方法
            const fallbackData = {
                timestamp: Date.now(),
                random: Math.random(),
                userAgent: "miniprogram",
                platform: "windows",
                version: "3.9.10",
                fallback: true
            };

            const fallbackStr = JSON.stringify(fallbackData);
            return CryptoJS.SHA256(fallbackStr).toString();
        }
    }

    /**
     * 生成API签名
     */
    generateApiSignature(method, path, data, timestamp, nonce) {
        try {
            if (!this.apiSignatureSecret) {
                console.warn("API签名密钥未初始化，使用备用密钥");
                this.apiSignatureSecret = this.fallbackSecret;
                this.apiKeyVersion = 1;
                this.apiKeyLastUpdate = Date.now();
            }

            let dataStr = "";
            if (data) {
                const sortedKeys = Object.keys(data).sort();
                const sortedData = {};
                sortedKeys.forEach(key => {
                    sortedData[key] = data[key];
                });
                dataStr = JSON.stringify(sortedData).replace(/\s/g, "");
            }

            const signString = `${method.toUpperCase()}\n${path}\n${dataStr}\n${timestamp}\n${nonce}`;
            return CryptoJS.HmacSHA256(signString, this.apiSignatureSecret).toString();
        } catch (error) {
            console.error("生成API签名失败:", error);
            return null;
        }
    }

    /**
     * 生成签名请求头
     */
    generateSignedHeaders(method, path, data) {
        const timestamp = Math.floor(Date.now() / 1000).toString();
        const nonce = Math.random().toString(36).substring(2, 18);
        const signature = this.generateApiSignature(method, path, data, timestamp, nonce);

        return {
            'Content-Type': 'application/json',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090819)',
            'Referer': 'https://servicewechat.com/wx421aabd7feefa0ed/devtools/page-frame.html',
            'Origin': 'https://servicewechat.com',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'cross-site',
            'X-YAN-Signature': signature,
            'X-YAN-Timestamp': timestamp,
            'X-YAN-Nonce': nonce
        };
    }

    /**
     * 创建广告会话
     */
    async createAdSession(projectId) {
        const deviceFingerprint = this.generateDeviceFingerprint();
        const data = {
            project_id: projectId,
            device_fingerprint: deviceFingerprint
        };


        const headers = this.generateSignedHeaders("POST", "/api/public/ad-session", data);

        // 添加额外的请求选项
        const requestOptions = {
            headers,
            timeout: 10000,
            validateStatus: function (status) {
                return status < 500; // 只有服务器错误才会被视为失败
            }
        };

        try {
            // 尝试最多3次
            for (let attempt = 1; attempt <= 3; attempt++) {
                try {
                    console.log(`尝试创建广告会话 (尝试 ${attempt}/3)...`);
                    const response = await axios.post(`${this.baseURL}/api/public/ad-session`, data, requestOptions);

                    if (response.status === 200 && response.data) {
                        if (response.data.error) {
                            throw new Error(response.data.error);
                        }
                        this.sessionId = response.data.session_id;
                        console.log(`创建广告会话成功: ${this.sessionId}`);
                        return response.data;
                    } else if (response.status === 429) {
                        console.log("频率限制，等待后重试...");
                        await new Promise(resolve => setTimeout(resolve, 2000));
                        continue;
                    } else if (response.status === 403) {
                        console.log("请求被拒绝 (403)，可能是签名无效或IP被封锁");

                        // 如果被拒绝，使用模拟会话
                        const mockSessionId = `mock_session_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
                        this.sessionId = mockSessionId;
                        return {
                            session_id: mockSessionId,
                            expires_at: new Date(Date.now() + 3600000).toISOString()
                        };
                    } else {
                        console.log(`请求返回状态码: ${response.status}`);
                        if (attempt < 3) {
                            await new Promise(resolve => setTimeout(resolve, 1000));
                            continue;
                        }
                        throw new Error(`HTTP状态码: ${response.status}`);
                    }
                } catch (attemptError) {
                    if (attempt < 3) {
                        console.log(`尝试 ${attempt} 失败: ${attemptError.message}，重试中...`);
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    } else {
                        throw attemptError;
                    }
                }
            }

            throw new Error("所有尝试均失败");
        } catch (error) {
            console.error("创建广告会话失败:", error.message);

            // 如果所有尝试都失败，使用模拟会话
            console.log("使用模拟会话数据作为备用");
            const mockSessionId = `mock_session_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
            this.sessionId = mockSessionId;
            return {
                session_id: mockSessionId,
                expires_at: new Date(Date.now() + 3600000).toISOString()
            };
        }
    }

    /**
     * 验证广告观看
     */
    async verifyAdCompletion(sessionId, watchDuration) {
        const data = {
            session_id: sessionId,
            watch_duration: watchDuration,
            completion_proof: "miniprogram_ad_completed"
        };

        const headers = this.generateSignedHeaders("POST", "/api/public/ad-verify", data);

        try {
            const response = await axios.post(`${this.baseURL}/api/public/ad-verify`, data, { headers });
            
            if (response.status === 200 && response.data) {
                if (response.data.error) {
                    throw new Error(response.data.error);
                }
                this.tempToken = response.data.temp_token;
                console.log("广告验证成功");
                return response.data;
            } else if (response.status === 429) {
                throw new Error("频率限制");
            } else {
                throw new Error("网络请求失败");
            }
        } catch (error) {
            console.error("验证广告观看失败:", error.message);
            throw error;
        }
    }

    /**
     * 获取验证码
     */
    async getVerificationCodeWithSession(projectId, sessionId, tempToken) {
        const data = {
            project_id: projectId,
            session_id: sessionId,
            temp_token: tempToken
        };

        const headers = this.generateSignedHeaders("POST", "/api/public/verification-code", data);

        try {
            const response = await axios.post(`${this.baseURL}/api/public/verification-code`, data, { headers });
            
            if (response.status === 200 && response.data) {
                if (response.data.error) {
                    throw new Error(response.data.error);
                }
                console.log("获取验证码成功");
                return response.data;
            } else {
                const errorMsg = response.data?.error || `请求失败 (${response.status})`;
                throw new Error(errorMsg);
            }
        } catch (error) {
            console.error("获取验证码失败:", error.message);
            throw error;
        }
    }

    /**
     * 使用临时密钥解密验证码数据
     */
    decryptVerificationDataWithTempKey(encryptedData, tempToken) {
        try {
            if (!encryptedData.startsWith("YAN_TEMP_") || !encryptedData.endsWith("_END")) {
                throw new Error("无效的加密数据格式");
            }

            const base64Data = encryptedData.slice(9, -4);
            const key = CryptoJS.enc.Base64.parse(tempToken);
            const encData = CryptoJS.enc.Base64.parse(base64Data);

            // 前16字节是IV，后面是密文
            const iv = CryptoJS.lib.WordArray.create(encData.words.slice(0, 4));
            const ciphertext = CryptoJS.lib.WordArray.create(encData.words.slice(4));

            const decrypted = CryptoJS.AES.decrypt(
                { ciphertext: ciphertext },
                key,
                {
                    iv: iv,
                    mode: CryptoJS.mode.CBC,
                    padding: CryptoJS.pad.Pkcs7
                }
            ).toString(CryptoJS.enc.Utf8);

            if (!decrypted) {
                throw new Error("解密结果为空");
            }

            return JSON.parse(decrypted);
        } catch (error) {
            console.error("解密失败:", error.message);
            throw new Error("解密失败");
        }
    }

    /**
     * 使用固定密钥解密验证码数据
     */
    decryptVerificationData(encryptedData) {
        try {
            if (!encryptedData.startsWith("YAN_AES_") || !encryptedData.endsWith("_END")) {
                throw new Error("数据格式错误");
            }

            const base64Data = encryptedData.slice(8, -4);
            const key = CryptoJS.SHA256("Yan&!SD#asui*+.Ge");
            const encData = CryptoJS.enc.Base64.parse(base64Data);

            const iv = CryptoJS.lib.WordArray.create(encData.words.slice(0, 4));
            const ciphertext = CryptoJS.lib.WordArray.create(encData.words.slice(4));

            const decrypted = CryptoJS.AES.decrypt(
                { ciphertext: ciphertext },
                key,
                {
                    iv: iv,
                    mode: CryptoJS.mode.CBC,
                    padding: CryptoJS.pad.Pkcs7
                }
            ).toString(CryptoJS.enc.Utf8);

            if (!decrypted) {
                throw new Error("解密结果为空");
            }

            return JSON.parse(decrypted);
        } catch (error) {
            console.error("解密失败:", error.message);
            throw new Error(`解密失败: ${error.message}`);
        }
    }

    /**
     * 完整的验证码获取流程
     */
    async getVerificationCode(projectId = "YCursor", simulateAdWatch = true) {
        try {
            console.log("开始获取验证码流程...");
            
            // 1. 获取签名密钥
            await this.fetchCurrentSignatureKey();
            
            // 2. 创建广告会话
            const sessionData = await this.createAdSession(projectId);
            
            if (simulateAdWatch) {
                // 3. 模拟观看广告（等待几秒）
                console.log("模拟观看广告...");
                await new Promise(resolve => setTimeout(resolve, 3000));
                
                // 4. 验证广告观看
                const watchDuration = 30000; // 30秒
                const verifyData = await this.verifyAdCompletion(this.sessionId, watchDuration);
                
                // 5. 获取验证码
                const codeData = await this.getVerificationCodeWithSession(projectId, this.sessionId, this.tempToken);
                
                // 6. 解密验证码
                const decryptedData = this.decryptVerificationDataWithTempKey(codeData.data, this.tempToken);
                
                console.log("验证码获取成功:", decryptedData.code);
                return {
                    code: decryptedData.code,
                    expires_at: decryptedData.expires_at,
                    sessionId: this.sessionId,
                    tempToken: this.tempToken
                };
            } else {
                console.log("需要观看广告才能获取验证码");
                return {
                    sessionId: this.sessionId,
                    message: "需要观看广告"
                };
            }
            
        } catch (error) {
            console.error("获取验证码失败:", error.message);
            throw error;
        }
    }
}

module.exports = VerificationCodeDecryptor;

// 使用示例
if (require.main === module) {
    const decryptor = new VerificationCodeDecryptor();
    
    decryptor.getVerificationCode("YAugment", true)
        .then(result => {
            console.log("最终结果:", result);
        })
        .catch(error => {
            console.error("错误:", error.message);
        });
}
