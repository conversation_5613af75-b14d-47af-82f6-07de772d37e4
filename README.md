# 微信小程序验证码解密方案

## 🎯 概述

这是一个基于对微信小程序代码逆向分析的验证码获取和解密工具。该小程序使用了**四阶段验证机制**，包含广告观看验证和IP地址一致性检查。

## ✅ 当前状态

- ✅ **API签名算法** - 完全破解
- ✅ **设备指纹生成** - 完全破解
- ✅ **验证码解密** - 完全破解
- ✅ **获取签名密钥** - 100%成功
- ✅ **创建广告会话** - 100%成功
- ❌ **验证广告观看** - IP地址限制
- ⏸️ **获取验证码** - 依赖广告验证

## 🔧 可用工具

1. **`practical_decryptor.js`** - 实用解密工具（推荐）
2. **`verification_code_decryptor.js`** - 完整解密工具类
3. **`complete_test.js`** - 完整流程测试
4. **`fix_403_test.js`** - 403错误修复测试
5. **`final_analysis_report.md`** - 详细技术分析报告

## 技术分析

### 验证流程

1. **获取签名密钥** - 从服务器获取API签名所需的密钥
2. **创建广告会话** - 创建一个广告观看会话
3. **验证广告观看** - 验证用户已完整观看广告
4. **获取验证码** - 获取加密的验证码数据
5. **解密验证码** - 使用临时密钥解密验证码

### 关键API端点

- `GET /api/public/signature-key` - 获取签名密钥
- `POST /api/public/ad-session` - 创建广告会话
- `POST /api/public/ad-verify` - 验证广告观看
- `POST /api/public/verification-code` - 获取验证码

### 加密算法

1. **API签名**: HMAC-SHA256
2. **设备指纹**: SHA256
3. **验证码加密**: AES-256-CBC

## 安装依赖

```bash
npm install crypto-js axios
```

## 🚀 推荐使用方法

由于IP地址一致性检查的限制，我们推荐使用以下方法：

### 1. 使用实用解密工具（推荐）

```javascript
const PracticalDecryptor = require('./practical_decryptor');

// 创建解密器
const decryptor = new PracticalDecryptor();

// 方法1: 解密临时密钥加密的数据
const encryptedData = "YAN_TEMP_base64data_END";
const tempToken = "your_temp_token";
const result = decryptor.decryptWithTempKey(encryptedData, tempToken);
console.log("验证码:", result.code);

// 方法2: 解密固定密钥加密的数据
const encryptedData2 = "YAN_AES_base64data_END";
const result2 = decryptor.decryptWithFixedKey(encryptedData2);
console.log("验证码:", result2.code);

// 方法3: 自动检测格式并解密
const result3 = decryptor.autoDecrypt(encryptedData, tempToken);
console.log("验证码:", result3.code);

// 方法4: 批量解密
const dataList = [
    { encryptedData: "YAN_TEMP_xxx_END", tempToken: "token1" },
    { encryptedData: "YAN_AES_xxx_END", tempToken: null }
];
const results = decryptor.batchDecrypt(dataList);
```

### 2. 半自动化方案

由于IP地址限制，建议在微信小程序环境中获取会话ID和临时令牌，然后使用我们的工具解密：

```javascript
// 1. 从微信小程序中获取session_id和temp_token
// 2. 使用我们的工具解密验证码
const PracticalDecryptor = require('./practical_decryptor');
const decryptor = new PracticalDecryptor();

// 从小程序获取的数据
const encryptedData = "YAN_TEMP_base64data_END";
const tempToken = "从小程序获取的临时令牌";

// 解密
const result = decryptor.decryptWithTempKey(encryptedData, tempToken);
console.log("验证码:", result.code);
console.log("过期时间:", result.expires_at);
```

### 3. 完整流程测试（仅供参考）

```javascript
const CompleteTest = require('./complete_test');
const tester = new CompleteTest();
tester.runCompleteTest().catch(console.error);
```

### 4. 修复403错误测试（仅供参考）

```javascript
const fix403 = require('./fix_403_test');
fix403.runTests().catch(console.error);
```

## API详细说明

### 类方法

#### `fetchCurrentSignatureKey()`
获取API签名密钥，如果失败会使用备用密钥。

#### `generateDeviceFingerprint()`
生成设备指纹，用于标识设备。

#### `generateApiSignature(method, path, data, timestamp, nonce)`
生成API请求签名。

#### `createAdSession(projectId)`
创建广告会话。
- `projectId`: 项目ID，如 "YCursor" 或 "YAugment"

#### `verifyAdCompletion(sessionId, watchDuration)`
验证广告观看完成。
- `sessionId`: 会话ID
- `watchDuration`: 观看时长（毫秒）

#### `getVerificationCodeWithSession(projectId, sessionId, tempToken)`
获取验证码数据。

#### `decryptVerificationDataWithTempKey(encryptedData, tempToken)`
使用临时密钥解密验证码。

#### `decryptVerificationData(encryptedData)`
使用固定密钥解密验证码。

## 重要说明

### 签名密钥

系统会自动从服务器获取最新的签名密钥：
- 主密钥格式: `YAN_API_SIGN_V6_2025_FORTRESS_LEVEL_KEY_*`
- 备用密钥: `YAN_API_SIGN_2025_ULTRA_SECRET_KEY_FOR_SIGNATURE_VERIFICATION_9f8e7d6c5b4a3210`

### 加密格式

验证码数据有两种加密格式：
1. `YAN_TEMP_[base64]_END` - 使用临时密钥加密
2. `YAN_AES_[base64]_END` - 使用固定密钥加密

### 频率限制

API有频率限制，请求过于频繁会返回429错误。建议：
- 不要短时间内重复请求
- 失败后等待一段时间再重试
- 合理使用缓存机制

## 错误处理

常见错误及处理方法：

- `Rate limit exceeded` - 请求频率过高，等待后重试
- `Session not found` - 会话不存在或已过期，重新创建会话
- `Invalid signature` - 签名验证失败，检查密钥和签名算法
- `解密失败` - 数据格式错误或密钥不匹配

## 注意事项

1. 此工具仅用于学习和研究目的
2. 请遵守相关服务的使用条款
3. 不要滥用API接口
4. 注意保护个人隐私和数据安全

## 技术细节

### 设备指纹算法

设备指纹包含以下信息：
- 设备型号、平台、系统版本
- 屏幕尺寸、像素比
- 应用ID、环境版本
- 时间戳、随机数
- 网络类型、品牌等

### API签名算法

签名字符串格式：
```
METHOD\n
PATH\n
SORTED_JSON_DATA\n
TIMESTAMP\n
NONCE
```

使用HMAC-SHA256算法生成签名。

### AES解密参数

- 算法: AES-256-CBC
- 填充: PKCS7
- IV: 密文前16字节
- 密钥: 临时令牌或固定密钥的SHA256值

## 许可证

本项目仅供学习研究使用，请勿用于商业用途。
